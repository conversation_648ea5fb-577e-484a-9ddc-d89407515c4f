package main

import (
	"bytes"
	"crypto/tls"
	"log"
	"net"
	"time"
)

func isHttpHeader(header []byte) bool {
	if bytes.HasPrefix(header, []byte("CONNECT")) == true ||
		bytes.HasPrefix(header, []byte("GET")) == true ||
		bytes.HasPrefix(header, []byte("POST")) == true ||
		bytes.HasPrefix(header, []byte("HEAD")) == true ||
		bytes.HasPrefix(header, []byte("PUT")) == true ||
		bytes.HasPrefix(header, []byte("COPY")) == true ||
		bytes.HasPrefix(header, []byte("DELETE")) == true ||
		bytes.HasPrefix(header, []byte("MOVE")) == true ||
		bytes.HasPrefix(header, []byte("OPTIONS")) == true ||
		bytes.HasPrefix(header, []byte("LINK")) == true ||
		bytes.HasPrefix(header, []byte("UNLINK")) == true ||
		bytes.HasPrefix(header, []byte("TRACE")) == true ||
		bytes.HasPrefix(header, []byte("PATCH")) == true ||
		bytes.HasPrefix(header, []byte("WRAPPED")) == true {
		return true
	}
	return false
}

func rspHeader(header []byte) []byte {
	if bytes.Contains(header, []byte("WebSocket")) == true {
		return []byte("HTTP/1.1 101 Switching Protocols\r\nUpgrade: websocket\r\nConnection: Upgrade\r\nSec-WebSocket-Accept: CuteBi Network Tunnel, (%>w<%)\r\n\r\n")
	} else if bytes.HasPrefix(header, []byte("CON")) == true {
		return []byte("HTTP/1.1 200 Connection established\r\nServer: CuteBi Network Tunnel, (%>w<%)\r\nConnection: keep-alive\r\n\r\n")
	} else {
		return []byte("HTTP/1.1 200 OK\r\nTransfer-Encoding: chunked\r\nServer: CuteBi Network Tunnel, (%>w<%)\r\nConnection: keep-alive\r\n\r\n")
	}
}

func handleTunnel(cConn net.Conn, payload []byte, tlsConfig *tls.Config) {
	defer tcpBufferPool.Put(payload)

	var payloadLen int
	for {
		cConn.SetReadDeadline(time.Now().Add(config.Tcp_timeout))
		RLen, err := cConn.Read(payload)
		if err != nil || RLen <= 0 {
			cConn.Close()
			return
		}
		payloadLen += RLen
		if !isHttpHeader(payload[:payloadLen]) || bytes.HasSuffix(payload[:payloadLen], []byte("\n\r\n")) {
			break
		}
	}

	// 用户认证
	var userID string
	if config.Enable_traffic_control && isHttpHeader(payload[:payloadLen]) {
		userID = authenticateFromHeader(payload[:payloadLen])
		if userID == "" {
			log.Println("用户认证失败")
			cConn.Write([]byte("HTTP/1.1 401 Unauthorized\r\nConnection: close\r\n\r\nAuthentication required"))
			cConn.Close()
			return
		}
	}

	if isHttpHeader(payload[:payloadLen]) == false {
		/* 转为tls的conn */
		if tlsConfig != nil {
			cConn = tls.Server(cConn, tlsConfig)
		}
		handleUdpSessionWithAuth(cConn, payload[:payloadLen], userID)
	} else {
		if config.Enable_httpDNS == false || Respond_HttpDNS(cConn, payload[:payloadLen]) == false { /*优先处理httpDNS请求*/
			if WLen, err := cConn.Write(rspHeader(payload[:payloadLen])); err != nil || WLen <= 0 {
				cConn.Close()
				return
			}
			/* 转为tls的conn */
			if tlsConfig != nil {
				cConn = tls.Server(cConn, tlsConfig)
			}
			if bytes.Contains(payload[:payloadLen], []byte(config.Udp_flag)) == true {
				handleUdpSessionWithAuth(cConn, nil, userID)
			} else {
				handleTcpSessionWithAuth(cConn, payload, userID)
			}
		}
	}
}

// 从HTTP头中提取用户认证信息
func authenticateFromHeader(header []byte) string {
	if !config.Enable_traffic_control {
		return ""
	}

	// 查找Authorization头
	authIndex := bytes.Index(header, []byte("\nAuthorization: Basic "))
	if authIndex == -1 {
		authIndex = bytes.Index(header, []byte("\nauthorization: basic "))
	}
	if authIndex == -1 {
		return ""
	}

	authIndex += len("\nAuthorization: Basic ")
	authEnd := bytes.IndexByte(header[authIndex:], '\r')
	if authEnd == -1 {
		return ""
	}
	authEnd += authIndex

	// 解码Base64认证信息
	authData := string(header[authIndex:authEnd])
	// 这里应该解码Base64并验证用户名密码
	// 为简化，假设直接从头部获取用户ID

	// 查找自定义的User-ID头
	userIDIndex := bytes.Index(header, []byte("\nUser-ID: "))
	if userIDIndex == -1 {
		userIDIndex = bytes.Index(header, []byte("\nuser-id: "))
	}
	if userIDIndex != -1 {
		userIDIndex += len("\nUser-ID: ")
		userIDEnd := bytes.IndexByte(header[userIDIndex:], '\r')
		if userIDEnd != -1 {
			userIDEnd += userIDIndex
			return string(header[userIDIndex:userIDEnd])
		}
	}

	return ""
}

func startHttpTunnel(listen_addr string) {
	var conn *net.TCPConn

	listener, err := net.Listen("tcp", listen_addr)
	if config.Enable_TFO {
		enableTcpFastopen(listener)
	}
	defer listener.Close()
	if err != nil {
		log.Println(err)
		return
	}
	tcpListener := listener.(*net.TCPListener)

	for {
		conn, err = tcpListener.AcceptTCP()
		if err != nil {
			log.Println(err)
			time.Sleep(3 * time.Second)
			continue
		}
		conn.SetKeepAlive(true)
		conn.SetKeepAlivePeriod(time.Minute)
		go handleTunnel(conn, tcpBufferPool.Get().([]byte), config.Tls.tlsConfig)
	}
}
